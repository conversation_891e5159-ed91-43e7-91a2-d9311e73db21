import { Request, Response } from 'express';
import { languageMiddleware } from '../client/src/server/language-middleware';
import fs from 'fs';
import path from 'path';
import { JSDOM } from 'jsdom';
import { Helmet } from 'react-helmet-async';

// 用户代理检测
const BOT_UA_KEYWORDS = [
  'googlebot',
  'bingbot',
  'yandex',
  'baiduspider',
  'twitterbot',
  'facebookexternalhit',
  'linkedinbot',
  'pinterest',
  'slackbot',
  'vkshare',
  'facebot',
  'outbrain',
  'w3c_validator',
  'crawler',
  'spider',
  'ahrefsbot',
  'mj12bot',
  'dotbot',
  'semrushbot'
];

// 检查是否是搜索引擎爬虫
export function isBot(req: Request): boolean {
  const ua = req.headers['user-agent']?.toLowerCase() || '';
  return BOT_UA_KEYWORDS.some(keyword => ua.includes(keyword));
}

// 路由配置
const ROUTES_CONFIG = {
  '/': {
    title: 'Text Case Converter - Modern Text Case Conversion Tool',
    description: 'Transform your text to uppercase, lowercase, title case and more with this powerful text conversion tool.',
    h1: 'Text Case Converter',
    content: 'A powerful tool to convert text between different cases including uppercase, lowercase, title case, sentence case, and more.'
  },
  '/privacy-policy': {
    title: 'Privacy Policy - Text Case Converter',
    description: 'Our privacy policy explains how we handle your data when you use our text case conversion tool.',
    h1: 'Privacy Policy',
    content: 'We respect your privacy and are committed to protecting your personal data.'
  },
  '/terms-of-service': {
    title: 'Terms of Service - Text Case Converter',
    description: 'Our terms of service outline the rules and guidelines for using our text case conversion tool.',
    h1: 'Terms of Service',
    content: 'By using our service, you agree to these terms and conditions.'
  }
};

// 语言配置
const SUPPORTED_LANGUAGES = ['en', 'es', 'fr', 'de', 'zh'];

// 获取路由配置
function getRouteConfig(path: string) {
  // 移除语言前缀
  let normalizedPath = path;
  for (const lang of SUPPORTED_LANGUAGES) {
    if (path.startsWith(`/${lang}/`)) {
      normalizedPath = path.substring(lang.length + 1);
      break;
    } else if (path === `/${lang}`) {
      normalizedPath = '/';
      break;
    }
  }
  
  // 返回配置或默认值
  return ROUTES_CONFIG[normalizedPath as keyof typeof ROUTES_CONFIG] || {
    title: 'Text Case Converter',
    description: 'Convert text between different cases easily.',
    h1: 'Text Case Converter',
    content: 'A text case conversion tool.'
  };
}

// 服务器端渲染中间件
// 先应用语言中间件处理语言选择
export async function ssrMiddleware(req: Request, res: Response, next: Function) {
  // 处理语言选择查询参数
  languageMiddleware(req, res, (err?: any) => {
    if (err) return next(err);
    // 如果没有重定向，继续SSR流程
    
    try {
      // 只为搜索引擎爬虫提供SSR
    if (!isBot(req)) {
      return next();
    }
    
    const url = req.originalUrl;
    
    // 读取HTML模板
    const indexPath = path.resolve(process.cwd(), 'dist/public/index.html');
    const template = fs.readFileSync(indexPath, 'utf-8');
    
    // 使用JSDOM解析HTML
    const dom = new JSDOM(template);
    const document = dom.window.document;
    
    // 获取路由配置
    const config = getRouteConfig(url);
    
    // 更新标题和描述
    const titleElement = document.querySelector('title');
    if (titleElement) {
      titleElement.textContent = config.title;
    }
    
    // 更新meta描述
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute('content', config.description);
    }
    
    // 更新Open Graph标签
    const ogTitle = document.querySelector('meta[property="og:title"]');
    const ogDescription = document.querySelector('meta[property="og:description"]');
    const ogUrl = document.querySelector('meta[property="og:url"]');
    
    if (ogTitle) ogTitle.setAttribute('content', config.title);
    if (ogDescription) ogDescription.setAttribute('content', config.description);
    if (ogUrl) ogUrl.setAttribute('content', `https://textcase.top${url}`);
    
    // 更新Twitter卡片
    const twitterTitle = document.querySelector('meta[name="twitter:title"]');
    const twitterDescription = document.querySelector('meta[name="twitter:description"]');
    
    if (twitterTitle) twitterTitle.setAttribute('content', config.title);
    if (twitterDescription) twitterDescription.setAttribute('content', config.description);
    
    // 添加预渲染内容
    const rootElement = document.getElementById('root');
    if (rootElement) {
      rootElement.innerHTML = `
        <div>
          <header>
            <nav>
              <a href="/">Home</a>
              <a href="/privacy-policy">Privacy Policy</a>
              <a href="/terms-of-service">Terms of Service</a>
            </nav>
          </header>
          <main>
            <h1>${config.h1}</h1>
            <p>${config.content}</p>
          </main>
          <footer>
            <p>© ${new Date().getFullYear()} Text Case Converter. All rights reserved.</p>
          </footer>
        </div>
      `;
    }
    
    // 添加canonical和hreflang标签
    const head = document.querySelector('head');
    if (head) {
      // 添加canonical链接
      const canonical = document.createElement('link');
      canonical.setAttribute('rel', 'canonical');
      canonical.setAttribute('href', `https://textcase.top${url}`);
      head.appendChild(canonical);
      
      // 添加hreflang标签
      SUPPORTED_LANGUAGES.forEach(lang => {
        const hreflang = document.createElement('link');
        hreflang.setAttribute('rel', 'alternate');
        hreflang.setAttribute('hreflang', lang);
        
        // 构建语言特定URL
        let langUrl = url;
        if (lang === 'en') {
          // 英语是默认语言，移除任何语言前缀
          for (const l of SUPPORTED_LANGUAGES) {
            if (url.startsWith(`/${l}/`)) {
              langUrl = url.substring(l.length + 1);
              break;
            } else if (url === `/${l}`) {
              langUrl = '/';
              break;
            }
          }
        } else {
          // 非英语语言，添加语言前缀
          // 首先移除任何现有语言前缀
          for (const l of SUPPORTED_LANGUAGES) {
            if (url.startsWith(`/${l}/`)) {
              langUrl = url.substring(l.length + 1);
              break;
            } else if (url === `/${l}`) {
              langUrl = '/';
              break;
            }
          }
          
          // 然后添加新的语言前缀
          langUrl = langUrl === '/' ? `/${lang}` : `/${lang}${langUrl}`;
        }
        
        hreflang.setAttribute('href', `https://textcase.top${langUrl}`);
        head.appendChild(hreflang);
      });
      
      // 添加x-default hreflang
      const defaultHreflang = document.createElement('link');
      defaultHreflang.setAttribute('rel', 'alternate');
      defaultHreflang.setAttribute('hreflang', 'x-default');
      defaultHreflang.setAttribute('href', 'https://textcase.top/');
      head.appendChild(defaultHreflang);
    }
    
    // 返回修改后的HTML
    res.send(dom.serialize());
    } catch (error) {
      console.error('SSR error:', error);
      next(error);
    }
  });
}
